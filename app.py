from scrapegraphai.graphs import SmartScraperGraph
import json
import re

# Configuración usando Groq (GRATIS con límites generosos)
graph_config = {
    "llm": {
        "model": "groq/llama3-8b-8192",  # Modelo Llama 3 8B en Groq
        "api_key": "********************************************************",  # Reemplaza con tu API key de Groq
        "temperature": 0.1,
    },
    "verbose": True,
    "headless": True,
    "timeout": 30,
    "output_format": "json",  # Forzar formato JSON
}

def clean_json_response(response_text):
    """Limpia y corrige respuestas JSON malformadas"""
    if isinstance(response_text, dict):
        return response_text

    if isinstance(response_text, str):
        # Buscar el JSON dentro del texto
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if json_match:
            json_str = json_match.group()
            # Limpiar saltos de línea dentro de strings
            json_str = re.sub(r'(?<="[^"]*)\n(?=[^"]*")', ' ', json_str)
            try:
                return json.loads(json_str)
            except json.JSONDecodeError:
                # Si aún falla, intentar reparar comillas
                json_str = json_str.replace('",\n"', '", "')
                try:
                    return json.loads(json_str)
                except:
                    return {"content": response_text}

    return {"content": str(response_text)}

# Crear instancia del SmartScraperGraph con prompt mejorado
smart_scraper_graph = SmartScraperGraph(
    prompt="""Extrae información sobre ScrapegraphAI y devuelve ÚNICAMENTE un objeto JSON válido con esta estructura exacta:
    {
        "descripcion": "descripción de qué es y para qué sirve",
        "instalacion": "instrucciones de instalación",
        "configuracion": "configuración básica y avanzada",
        "ejemplos": "ejemplos de código prácticos",
        "tipos_scrapers": "diferentes tipos de scrapers disponibles",
        "guias": "guías de uso y mejores prácticas",
        "apis": "información sobre APIs y servicios",
        "precios": "precios y planes disponibles"
    }

    IMPORTANTE: Responde SOLO con JSON válido, sin texto adicional antes o después.""",
    source="https://scrapegraphai.com/",  # Página principal más confiable
    config=graph_config
)

print("🚀 Iniciando scraping con ScrapegraphAI + Groq...")
print("📄 Extrayendo información de:", smart_scraper_graph.source)

try:
    # Ejecutar el pipeline
    result = smart_scraper_graph.run()

    # Limpiar y procesar la respuesta
    cleaned_result = clean_json_response(result)

    print("\n✅ Scraping completado exitosamente!")
    print("📊 Resultado:")
    print("=" * 80)
    print(json.dumps(cleaned_result, indent=4, ensure_ascii=False))

    # Guardar resultado en archivo
    with open("scrapegraphai_info.json", "w", encoding="utf-8") as f:
        json.dump(cleaned_result, f, indent=4, ensure_ascii=False)
    print(f"\n💾 Resultado guardado en: scrapegraphai_info.json")

except Exception as e:
    print(f"\n❌ Error durante el scraping: {e}")
    print("\n🔧 Posibles soluciones:")
    print("1. Verifica que tu API key de Groq sea válida")
    print("2. Asegúrate de tener créditos disponibles en Groq")
    print("3. Revisa tu conexión a internet")
    print("4. Intenta con una URL diferente")

    # Configuración alternativa con modelo más pequeño y prompt simplificado
    print("\n🔄 Intentando con configuración alternativa...")

    alternative_config = {
        "llm": {
            "model": "groq/llama3-70b-8192",  # Modelo más potente
            "api_key": "********************************************************",
            "temperature": 0,
        },
        "verbose": False,
        "headless": True,
        "timeout": 20,
        "output_format": "json",
    }

    try:
        alternative_scraper = SmartScraperGraph(
            prompt="""Extrae información básica sobre ScrapegraphAI y responde SOLO con JSON válido:
            {
                "nombre": "nombre del proyecto",
                "descripcion": "descripción breve",
                "caracteristicas": "características principales"
            }""",
            source="https://github.com/VinciGit00/Scrapegraph-ai",  # Fuente alternativa
            config=alternative_config
        )

        result = alternative_scraper.run()
        cleaned_result = clean_json_response(result)
        print("✅ Configuración alternativa exitosa!")
        print(json.dumps(cleaned_result, indent=4, ensure_ascii=False))

        # Guardar resultado alternativo
        with open("scrapegraphai_info_alt.json", "w", encoding="utf-8") as f:
            json.dump(cleaned_result, f, indent=4, ensure_ascii=False)
        print(f"\n💾 Resultado alternativo guardado en: scrapegraphai_info_alt.json")

    except Exception as e2:
        print(f"❌ Error en configuración alternativa: {e2}")
        print("\n📋 Para obtener una API key gratuita de Groq:")
        print("1. Ve a: https://console.groq.com/")
        print("2. Regístrate gratis")
        print("3. Ve a 'API Keys' y crea una nueva")
        print("4. Reemplaza la API key en este script")

        # Último intento con configuración muy básica
        print("\n🔄 Último intento con configuración básica...")
        try:
            basic_config = {
                "llm": {
                    "model": "groq/llama3-8b-8192",
                    "api_key": "********************************************************",
                    "temperature": 0,
                },
                "verbose": False,
                "headless": True,
                "timeout": 15,
            }

            basic_scraper = SmartScraperGraph(
                prompt="Extrae el título principal de esta página web",
                source="https://example.com",
                config=basic_config
            )

            result = basic_scraper.run()
            print("✅ Configuración básica funciona!")
            print(f"📄 Resultado básico: {result}")

        except Exception as e3:
            print(f"❌ Error en configuración básica: {e3}")
            print("🚨 Problema con la configuración de Groq o la conexión")