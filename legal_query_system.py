"""
Sistema de Consulta de Base de Conocimiento Legal
Permite consultar la base de conocimiento creada por legal_knowledge_base.py
Usa ScrapegraphAI + Groq para responder preguntas sobre las leyes procesadas
"""

import json
import os
from pathlib import Path
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime

from scrapegraphai.graphs import SmartScraperGraph

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LegalQuerySystem:
    """Sistema de consultas sobre la base de conocimiento legal"""
    
    def __init__(self, groq_api_key: str, knowledge_base_folder: str = "knowledge_base"):
        """
        Inicializa el sistema de consultas
        
        Args:
            groq_api_key: API key de Groq
            knowledge_base_folder: Carpeta con la base de conocimiento
        """
        self.groq_api_key = groq_api_key
        self.kb_folder = Path(knowledge_base_folder)
        
        # Configuración optimizada para consultas
        self.graph_config = {
            "llm": {
                "model": "groq/llama3-70b-8192",
                "api_key": groq_api_key,
                "temperature": 0.2,  # Temperatura baja para respuestas precisas
            },
            "verbose": False,
            "headless": True,
            "timeout": 60,
        }
        
        # Cargar la base de conocimiento
        self.load_knowledge_base()
    
    def load_knowledge_base(self):
        """Carga la base de conocimiento en memoria"""
        try:
            # Cargar índice general
            index_path = self.kb_folder / "knowledge_base_index.json"
            if index_path.exists():
                with open(index_path, 'r', encoding='utf-8') as f:
                    self.index = json.load(f)
                logger.info(f"✅ Índice cargado: {self.index['total_documents']} documentos")
            else:
                raise FileNotFoundError("No se encontró knowledge_base_index.json")
            
            # Cargar base de datos de artículos
            articles_path = self.kb_folder / "articles_database.json"
            if articles_path.exists():
                with open(articles_path, 'r', encoding='utf-8') as f:
                    self.articles_db = json.load(f)
                logger.info(f"✅ Artículos cargados: {self.articles_db['total_articles']} artículos")
            else:
                raise FileNotFoundError("No se encontró articles_database.json")
            
            # Cargar documentos individuales
            self.documents = {}
            for doc_info in self.index['documents']:
                doc_filename = doc_info['filename'].replace('.pdf', '_processed.json')
                doc_path = self.kb_folder / doc_filename
                
                if doc_path.exists():
                    with open(doc_path, 'r', encoding='utf-8') as f:
                        self.documents[doc_info['filename']] = json.load(f)
                
            logger.info(f"✅ Base de conocimiento cargada completamente")
            
        except Exception as e:
            logger.error(f"❌ Error cargando base de conocimiento: {e}")
            raise
    
    def search_articles_by_theme(self, theme: str) -> List[Dict[str, Any]]:
        """
        Busca artículos por tema
        
        Args:
            theme: Tema a buscar
            
        Returns:
            Lista de artículos relacionados con el tema
        """
        matching_articles = []
        
        for article in self.articles_db['articles']:
            # Buscar en temas y palabras clave
            article_themes = [t.lower() for t in article.get('themes', [])]
            article_keywords = [k.lower() for k in article.get('keywords', [])]
            
            if (theme.lower() in ' '.join(article_themes) or 
                theme.lower() in ' '.join(article_keywords) or
                theme.lower() in article.get('content', '').lower()):
                matching_articles.append(article)
        
        return matching_articles
    
    def search_articles_by_content(self, query: str) -> List[Dict[str, Any]]:
        """
        Busca artículos por contenido
        
        Args:
            query: Consulta de búsqueda
            
        Returns:
            Lista de artículos que contienen la consulta
        """
        matching_articles = []
        query_lower = query.lower()
        
        for article in self.articles_db['articles']:
            content = article.get('content', '').lower()
            title = article.get('article_title', '').lower()
            
            if query_lower in content or query_lower in title:
                matching_articles.append(article)
        
        return matching_articles
    
    def get_document_summary(self, document_filename: str) -> Optional[Dict[str, Any]]:
        """
        Obtiene el resumen de un documento específico
        
        Args:
            document_filename: Nombre del archivo del documento
            
        Returns:
            Información del documento o None si no se encuentra
        """
        if document_filename in self.documents:
            return self.documents[document_filename]
        return None
    
    def answer_legal_question(self, question: str, context_articles: List[Dict[str, Any]] = None) -> str:
        """
        Responde una pregunta legal usando la base de conocimiento
        
        Args:
            question: Pregunta legal
            context_articles: Artículos relevantes como contexto (opcional)
            
        Returns:
            Respuesta generada por el LLM
        """
        # Si no se proporcionan artículos de contexto, buscar automáticamente
        if context_articles is None:
            # Buscar artículos relevantes basados en palabras clave de la pregunta
            context_articles = self.search_articles_by_content(question)
            
            # Limitar a los 10 artículos más relevantes
            context_articles = context_articles[:10]
        
        if not context_articles:
            return "No se encontraron artículos relevantes en la base de conocimiento para responder esta pregunta."
        
        # Construir el contexto
        context_text = "CONTEXTO LEGAL:\n\n"
        for i, article in enumerate(context_articles, 1):
            context_text += f"ARTÍCULO {i}:\n"
            context_text += f"Documento: {article['document_title']}\n"
            context_text += f"Artículo: {article['article_number']}\n"
            context_text += f"Contenido: {article['content']}\n"
            context_text += f"Temas: {', '.join(article.get('themes', []))}\n\n"
        
        # Crear el prompt para responder la pregunta
        legal_prompt = f"""
        Eres un asistente legal especializado. Basándote ÚNICAMENTE en el contexto legal proporcionado, 
        responde la siguiente pregunta de manera precisa y fundamentada.
        
        PREGUNTA: {question}
        
        {context_text}
        
        INSTRUCCIONES:
        1. Responde basándote SOLO en la información proporcionada en el contexto
        2. Cita específicamente los artículos relevantes
        3. Si la información no es suficiente, indícalo claramente
        4. Proporciona una respuesta estructurada y profesional
        5. Incluye referencias a los documentos legales específicos
        
        RESPUESTA:
        """
        
        try:
            # Para consultas, usamos un enfoque más directo con el contexto
            # ya que no estamos scrapeando una web sino analizando texto legal

            # Crear un documento temporal con el contexto
            temp_doc_path = self.kb_folder / "temp_query_context.txt"
            with open(temp_doc_path, 'w', encoding='utf-8') as f:
                f.write(legal_prompt)

            scraper = SmartScraperGraph(
                prompt="Analiza el contexto legal proporcionado y responde la pregunta de manera fundamentada, citando específicamente los artículos relevantes",
                source=str(temp_doc_path),
                config=self.graph_config
            )

            result = scraper.run()

            # Limpiar archivo temporal
            if temp_doc_path.exists():
                temp_doc_path.unlink()

            if result and 'content' in result:
                return result['content']
            else:
                return "No se pudo generar una respuesta. Intenta reformular tu pregunta."

        except Exception as e:
            logger.error(f"Error generando respuesta: {e}")
            return f"Error al procesar la consulta: {e}"
    
    def interactive_query_session(self):
        """Sesión interactiva de consultas"""
        print("\n🏛️  Sistema de Consulta Legal Interactivo")
        print("=" * 60)
        print(f"📚 Base de conocimiento cargada:")
        print(f"   - Documentos: {self.index['total_documents']}")
        print(f"   - Artículos: {self.articles_db['total_articles']}")
        print(f"   - Temas: {len(self.index['all_themes'])}")
        print("\n💡 Comandos disponibles:")
        print("   - 'temas': Ver todos los temas disponibles")
        print("   - 'documentos': Ver lista de documentos")
        print("   - 'buscar [tema]': Buscar artículos por tema")
        print("   - 'salir': Terminar sesión")
        print("   - O simplemente haz una pregunta legal")
        
        while True:
            print("\n" + "-" * 60)
            user_input = input("🔍 Tu consulta: ").strip()
            
            if not user_input:
                continue
            
            if user_input.lower() == 'salir':
                print("👋 ¡Hasta luego!")
                break
            
            elif user_input.lower() == 'temas':
                print("\n📋 Temas disponibles:")
                for i, theme in enumerate(sorted(self.index['all_themes']), 1):
                    print(f"   {i:2d}. {theme}")
            
            elif user_input.lower() == 'documentos':
                print("\n📄 Documentos en la base de conocimiento:")
                for i, doc in enumerate(self.index['documents'], 1):
                    print(f"   {i:2d}. {doc['title']} ({doc['article_count']} artículos)")
            
            elif user_input.lower().startswith('buscar '):
                theme = user_input[7:].strip()
                articles = self.search_articles_by_theme(theme)
                
                if articles:
                    print(f"\n📖 Encontrados {len(articles)} artículos sobre '{theme}':")
                    for i, article in enumerate(articles[:5], 1):  # Mostrar solo los primeros 5
                        print(f"\n   {i}. {article['article_number']} - {article['document_title']}")
                        print(f"      {article['content'][:200]}...")
                else:
                    print(f"❌ No se encontraron artículos sobre '{theme}'")
            
            else:
                # Tratar como pregunta legal
                print("\n🤔 Analizando tu pregunta...")
                response = self.answer_legal_question(user_input)
                print(f"\n📋 Respuesta:\n{response}")

def main():
    """Función principal"""
    print("🏛️  Sistema de Consulta de Base de Conocimiento Legal")
    print("=" * 70)
    
    # Verificar que existe la base de conocimiento
    kb_folder = input("📁 Carpeta de base de conocimiento (default: 'knowledge_base'): ").strip() or "knowledge_base"
    
    if not Path(kb_folder).exists():
        print(f"❌ No se encontró la carpeta '{kb_folder}'")
        print("💡 Primero ejecuta 'legal_knowledge_base.py' para crear la base de conocimiento")
        return
    
    # Verificar archivos necesarios
    required_files = ["knowledge_base_index.json", "articles_database.json"]
    missing_files = []
    
    for file in required_files:
        if not (Path(kb_folder) / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Archivos faltantes en '{kb_folder}':")
        for file in missing_files:
            print(f"   - {file}")
        print("💡 Ejecuta 'legal_knowledge_base.py' para generar estos archivos")
        return
    
    # Obtener API key
    groq_api_key = input("🔑 Ingresa tu API key de Groq: ").strip()
    
    if not groq_api_key:
        print("❌ API key requerida")
        return
    
    try:
        # Crear sistema de consultas
        query_system = LegalQuerySystem(groq_api_key, kb_folder)
        
        # Iniciar sesión interactiva
        query_system.interactive_query_session()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
