"""
Herramienta de Construcción de Base de Conocimiento Legal
Procesa múltiples PDFs de leyes usando ScrapegraphAI + Groq
Extrae artículos, temáticas y estructura para consumo por LLMs
"""

import os
import json
import glob
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging
from dataclasses import dataclass, asdict

from scrapegraphai.graphs import SmartScraperGraph
from pydantic import BaseModel, Field

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class LegalDocument:
    """Representa un documento legal procesado"""
    filename: str
    title: str
    type: str  # "ley", "decreto", "reglamento", etc.
    date: Optional[str]
    articles: List[Dict[str, Any]]
    themes: List[str]
    summary: str
    processed_at: str

class ArticleSchema(BaseModel):
    """Esquema para un artículo legal"""
    number: str = Field(description="Número del artículo (ej: 'Artículo 1', 'Art. 15')")
    title: str = Field(description="Título o encabezado del artículo si existe")
    content: str = Field(description="Contenido completo del artículo")
    themes: List[str] = Field(description="Temas principales que aborda este artículo")
    keywords: List[str] = Field(description="Palabras clave importantes del artículo")

class LegalDocumentSchema(BaseModel):
    """Esquema para un documento legal completo"""
    title: str = Field(description="Título completo de la ley o documento")
    document_type: str = Field(description="Tipo de documento: ley, decreto, reglamento, etc.")
    date: str = Field(description="Fecha de promulgación o publicación")
    summary: str = Field(description="Resumen ejecutivo del documento")
    main_themes: List[str] = Field(description="Temas principales que aborda el documento")
    articles: List[ArticleSchema] = Field(description="Lista de artículos extraídos")
    total_articles: int = Field(description="Número total de artículos en el documento")

class LegalKnowledgeBase:
    """Procesador principal de documentos legales"""
    
    def __init__(self, groq_api_key: str, input_folder: str = "pdfs_leyes", output_folder: str = "knowledge_base"):
        """
        Inicializa el procesador
        
        Args:
            groq_api_key: API key de Groq
            input_folder: Carpeta con los PDFs a procesar
            output_folder: Carpeta donde guardar los resultados
        """
        self.groq_api_key = groq_api_key
        self.input_folder = Path(input_folder)
        self.output_folder = Path(output_folder)
        
        # Crear carpetas si no existen
        self.input_folder.mkdir(exist_ok=True)
        self.output_folder.mkdir(exist_ok=True)
        
        # Configuración de ScrapegraphAI con Groq
        self.graph_config = {
            "llm": {
                "model": "groq/llama3-70b-8192",  # Modelo más potente para documentos legales
                "api_key": groq_api_key,
                "temperature": 0.1,  # Baja temperatura para mayor precisión
            },
            "verbose": True,
            "headless": True,
            "timeout": 120,  # Timeout más largo para documentos complejos
        }
        
        self.processed_documents: List[LegalDocument] = []
    
    def find_pdf_files(self) -> List[Path]:
        """Encuentra todos los archivos PDF en la carpeta de entrada"""
        pdf_files = list(self.input_folder.glob("*.pdf"))
        logger.info(f"Encontrados {len(pdf_files)} archivos PDF")
        return pdf_files
    
    def process_single_pdf(self, pdf_path: Path) -> Optional[LegalDocument]:
        """
        Procesa un único archivo PDF
        
        Args:
            pdf_path: Ruta al archivo PDF
            
        Returns:
            LegalDocument procesado o None si hay error
        """
        logger.info(f"Procesando: {pdf_path.name}")
        
        try:
            # Crear el prompt específico para documentos legales
            legal_prompt = f"""
            Analiza este documento legal PDF y extrae la siguiente información estructurada:
            
            1. INFORMACIÓN GENERAL:
               - Título completo del documento
               - Tipo de documento (ley, decreto, reglamento, etc.)
               - Fecha de promulgación o publicación
               - Resumen ejecutivo del documento
            
            2. TEMAS PRINCIPALES:
               - Lista de los temas principales que aborda el documento
               - Materias jurídicas involucradas
            
            3. ARTÍCULOS:
               - Extrae TODOS los artículos del documento
               - Para cada artículo incluye:
                 * Número del artículo
                 * Título (si existe)
                 * Contenido completo
                 * Temas que aborda
                 * Palabras clave importantes
            
            4. ESTRUCTURA:
               - Número total de artículos
               - Organización del documento (capítulos, títulos, etc.)
            
            Responde en español y asegúrate de capturar todos los artículos sin omitir ninguno.
            """
            
            # Crear el scraper para documentos
            scraper = SmartScraperGraph(
                prompt=legal_prompt,
                source=str(pdf_path),  # ScrapegraphAI puede procesar PDFs directamente
                config=self.graph_config
            )
            
            # Ejecutar el procesamiento
            result = scraper.run()
            
            if not result or 'content' not in result:
                logger.error(f"No se pudo extraer contenido de {pdf_path.name}")
                return None
            
            # Procesar el resultado
            content = result['content']
            
            # Crear el documento legal
            legal_doc = LegalDocument(
                filename=pdf_path.name,
                title=content.get('title', pdf_path.stem),
                type=content.get('document_type', 'documento legal'),
                date=content.get('date', 'fecha no especificada'),
                articles=content.get('articles', []),
                themes=content.get('main_themes', []),
                summary=content.get('summary', 'resumen no disponible'),
                processed_at=datetime.now().isoformat()
            )
            
            logger.info(f"✅ Procesado exitosamente: {pdf_path.name}")
            logger.info(f"   - Artículos extraídos: {len(legal_doc.articles)}")
            logger.info(f"   - Temas identificados: {len(legal_doc.themes)}")
            
            return legal_doc
            
        except Exception as e:
            logger.error(f"❌ Error procesando {pdf_path.name}: {e}")
            return None
    
    def process_all_pdfs(self) -> List[LegalDocument]:
        """
        Procesa todos los PDFs en la carpeta de entrada
        
        Returns:
            Lista de documentos legales procesados
        """
        pdf_files = self.find_pdf_files()
        
        if not pdf_files:
            logger.warning("No se encontraron archivos PDF para procesar")
            return []
        
        logger.info(f"Iniciando procesamiento de {len(pdf_files)} documentos...")
        
        processed_docs = []
        
        for i, pdf_path in enumerate(pdf_files, 1):
            logger.info(f"\n📄 Procesando documento {i}/{len(pdf_files)}: {pdf_path.name}")
            
            legal_doc = self.process_single_pdf(pdf_path)
            
            if legal_doc:
                processed_docs.append(legal_doc)
                self.save_individual_document(legal_doc)
            
            # Pausa entre documentos para evitar rate limits
            if i < len(pdf_files):
                logger.info("⏸️  Pausa de 2 segundos...")
                import time
                time.sleep(2)
        
        self.processed_documents = processed_docs
        logger.info(f"\n🎉 Procesamiento completado: {len(processed_docs)}/{len(pdf_files)} documentos exitosos")
        
        return processed_docs
    
    def save_individual_document(self, legal_doc: LegalDocument):
        """Guarda un documento individual en formato JSON"""
        filename = f"{legal_doc.filename.replace('.pdf', '')}_processed.json"
        filepath = self.output_folder / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(asdict(legal_doc), f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 Guardado: {filepath}")
    
    def create_knowledge_base_index(self):
        """Crea un índice general de la base de conocimiento"""
        if not self.processed_documents:
            logger.warning("No hay documentos procesados para indexar")
            return
        
        # Crear índice general
        index = {
            "created_at": datetime.now().isoformat(),
            "total_documents": len(self.processed_documents),
            "documents": [],
            "all_themes": set(),
            "total_articles": 0
        }
        
        for doc in self.processed_documents:
            doc_info = {
                "filename": doc.filename,
                "title": doc.title,
                "type": doc.type,
                "date": doc.date,
                "article_count": len(doc.articles),
                "themes": doc.themes,
                "summary": doc.summary[:200] + "..." if len(doc.summary) > 200 else doc.summary
            }
            
            index["documents"].append(doc_info)
            index["all_themes"].update(doc.themes)
            index["total_articles"] += len(doc.articles)
        
        # Convertir set a lista para JSON
        index["all_themes"] = sorted(list(index["all_themes"]))
        
        # Guardar índice
        index_path = self.output_folder / "knowledge_base_index.json"
        with open(index_path, 'w', encoding='utf-8') as f:
            json.dump(index, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📚 Índice creado: {index_path}")
        logger.info(f"   - Total documentos: {index['total_documents']}")
        logger.info(f"   - Total artículos: {index['total_articles']}")
        logger.info(f"   - Temas únicos: {len(index['all_themes'])}")
    
    def create_articles_database(self):
        """Crea una base de datos consolidada de todos los artículos"""
        if not self.processed_documents:
            logger.warning("No hay documentos procesados")
            return
        
        articles_db = {
            "created_at": datetime.now().isoformat(),
            "total_articles": 0,
            "articles": []
        }
        
        for doc in self.processed_documents:
            for article in doc.articles:
                article_entry = {
                    "source_document": doc.filename,
                    "document_title": doc.title,
                    "document_type": doc.type,
                    "article_number": article.get('number', 'N/A'),
                    "article_title": article.get('title', ''),
                    "content": article.get('content', ''),
                    "themes": article.get('themes', []),
                    "keywords": article.get('keywords', [])
                }
                
                articles_db["articles"].append(article_entry)
        
        articles_db["total_articles"] = len(articles_db["articles"])
        
        # Guardar base de datos de artículos
        articles_path = self.output_folder / "articles_database.json"
        with open(articles_path, 'w', encoding='utf-8') as f:
            json.dump(articles_db, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📖 Base de datos de artículos creada: {articles_path}")
        logger.info(f"   - Total artículos: {articles_db['total_articles']}")
    
    def generate_summary_report(self):
        """Genera un reporte resumen del procesamiento"""
        if not self.processed_documents:
            logger.warning("No hay documentos procesados para el reporte")
            return
        
        report = {
            "processing_summary": {
                "date": datetime.now().isoformat(),
                "total_documents_processed": len(self.processed_documents),
                "total_articles_extracted": sum(len(doc.articles) for doc in self.processed_documents),
                "unique_themes": len(set().union(*[doc.themes for doc in self.processed_documents])),
            },
            "documents_detail": []
        }
        
        for doc in self.processed_documents:
            doc_detail = {
                "filename": doc.filename,
                "title": doc.title,
                "type": doc.type,
                "articles_count": len(doc.articles),
                "themes_count": len(doc.themes),
                "processing_date": doc.processed_at
            }
            report["documents_detail"].append(doc_detail)
        
        # Guardar reporte
        report_path = self.output_folder / "processing_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📊 Reporte generado: {report_path}")
        
        return report

def main():
    """Función principal"""
    print("🏛️  Herramienta de Construcción de Base de Conocimiento Legal")
    print("=" * 70)
    
    # Verificar API key
    groq_api_key = input("🔑 Ingresa tu API key de Groq: ").strip()
    
    if not groq_api_key:
        print("❌ API key requerida")
        return
    
    # Configurar carpetas
    input_folder = input("📁 Carpeta con PDFs (default: 'pdfs_leyes'): ").strip() or "pdfs_leyes"
    output_folder = input("💾 Carpeta de salida (default: 'knowledge_base'): ").strip() or "knowledge_base"
    
    # Crear procesador
    processor = LegalKnowledgeBase(groq_api_key, input_folder, output_folder)
    
    # Verificar que existan PDFs
    pdf_files = processor.find_pdf_files()
    if not pdf_files:
        print(f"❌ No se encontraron archivos PDF en '{input_folder}'")
        print(f"💡 Coloca tus archivos PDF de leyes en la carpeta '{input_folder}'")
        return
    
    print(f"\n📄 Encontrados {len(pdf_files)} archivos PDF:")
    for pdf in pdf_files:
        print(f"   - {pdf.name}")
    
    # Confirmar procesamiento
    confirm = input(f"\n¿Procesar {len(pdf_files)} documentos? (y/n): ").strip().lower()
    if confirm != 'y':
        print("❌ Procesamiento cancelado")
        return
    
    # Procesar documentos
    print("\n🚀 Iniciando procesamiento...")
    processed_docs = processor.process_all_pdfs()
    
    if processed_docs:
        print("\n📚 Creando índices y bases de datos...")
        processor.create_knowledge_base_index()
        processor.create_articles_database()
        report = processor.generate_summary_report()
        
        print(f"\n✅ ¡Procesamiento completado!")
        print(f"📊 Documentos procesados: {len(processed_docs)}")
        print(f"📖 Total artículos extraídos: {sum(len(doc.articles) for doc in processed_docs)}")
        print(f"💾 Archivos generados en: {output_folder}/")
        print(f"   - knowledge_base_index.json (índice general)")
        print(f"   - articles_database.json (base de datos de artículos)")
        print(f"   - processing_report.json (reporte del procesamiento)")
        print(f"   - [documento]_processed.json (cada documento individual)")
        
    else:
        print("❌ No se pudo procesar ningún documento")

if __name__ == "__main__":
    main()
