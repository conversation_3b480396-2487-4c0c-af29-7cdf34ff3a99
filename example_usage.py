"""
Ejemplo de uso programático de la Base de Conocimiento Legal
Demuestra cómo integrar el sistema en otras aplicaciones
"""

import json
import os
from pathlib import Path
from legal_query_system import LegalQuerySystem

def example_basic_usage():
    """Ejemplo básico de uso del sistema"""
    print("🔍 Ejemplo 1: Uso básico del sistema")
    print("-" * 50)
    
    # Configurar API key (en producción, usar variables de entorno)
    groq_api_key = os.getenv("GROQ_API_KEY", "tu_api_key_aqui")
    
    if groq_api_key == "tu_api_key_aqui":
        print("⚠️  Configura tu GROQ_API_KEY en las variables de entorno")
        return
    
    try:
        # Inicializar el sistema
        query_system = LegalQuerySystem(groq_api_key, "knowledge_base")
        
        # Hacer una consulta simple
        question = "¿Qué dice la ley sobre contratos laborales?"
        response = query_system.answer_legal_question(question)
        
        print(f"Pregunta: {question}")
        print(f"Respuesta: {response}")
        
    except Exception as e:
        print(f"Error: {e}")

def example_search_by_theme():
    """Ejemplo de búsqueda por tema"""
    print("\n🔍 Ejemplo 2: Búsqueda por tema")
    print("-" * 50)
    
    groq_api_key = os.getenv("GROQ_API_KEY", "tu_api_key_aqui")
    
    if groq_api_key == "tu_api_key_aqui":
        print("⚠️  Configura tu GROQ_API_KEY")
        return
    
    try:
        query_system = LegalQuerySystem(groq_api_key, "knowledge_base")
        
        # Buscar artículos sobre un tema específico
        theme = "contratos"
        articles = query_system.search_articles_by_theme(theme)
        
        print(f"Tema buscado: {theme}")
        print(f"Artículos encontrados: {len(articles)}")
        
        # Mostrar los primeros 3 artículos
        for i, article in enumerate(articles[:3], 1):
            print(f"\n{i}. {article['article_number']} - {article['document_title']}")
            print(f"   Contenido: {article['content'][:100]}...")
            print(f"   Temas: {', '.join(article.get('themes', []))}")
        
    except Exception as e:
        print(f"Error: {e}")

def example_batch_queries():
    """Ejemplo de consultas en lote"""
    print("\n🔍 Ejemplo 3: Consultas en lote")
    print("-" * 50)
    
    groq_api_key = os.getenv("GROQ_API_KEY", "tu_api_key_aqui")
    
    if groq_api_key == "tu_api_key_aqui":
        print("⚠️  Configura tu GROQ_API_KEY")
        return
    
    try:
        query_system = LegalQuerySystem(groq_api_key, "knowledge_base")
        
        # Lista de preguntas frecuentes
        questions = [
            "¿Cuáles son los derechos básicos del trabajador?",
            "¿Qué sanciones existen por incumplimiento de contratos?",
            "¿Cuál es el procedimiento para presentar una demanda?",
            "¿Qué requisitos debe cumplir un contrato válido?"
        ]
        
        results = []
        
        for i, question in enumerate(questions, 1):
            print(f"\n{i}. Procesando: {question}")
            response = query_system.answer_legal_question(question)
            
            result = {
                "question": question,
                "response": response,
                "timestamp": "2024-01-01T00:00:00"  # En producción, usar datetime.now()
            }
            
            results.append(result)
            print(f"   ✅ Respuesta generada ({len(response)} caracteres)")
        
        # Guardar resultados
        with open("batch_query_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Resultados guardados en: batch_query_results.json")
        
    except Exception as e:
        print(f"Error: {e}")

def example_knowledge_base_stats():
    """Ejemplo de estadísticas de la base de conocimiento"""
    print("\n📊 Ejemplo 4: Estadísticas de la base de conocimiento")
    print("-" * 50)
    
    try:
        # Cargar índice
        index_path = Path("knowledge_base/knowledge_base_index.json")
        if not index_path.exists():
            print("❌ No se encontró la base de conocimiento")
            return
        
        with open(index_path, 'r', encoding='utf-8') as f:
            index = json.load(f)
        
        # Cargar base de datos de artículos
        articles_path = Path("knowledge_base/articles_database.json")
        with open(articles_path, 'r', encoding='utf-8') as f:
            articles_db = json.load(f)
        
        # Mostrar estadísticas
        print(f"📚 Total de documentos: {index['total_documents']}")
        print(f"📖 Total de artículos: {articles_db['total_articles']}")
        print(f"🏷️  Temas únicos: {len(index['all_themes'])}")
        
        # Estadísticas por documento
        print(f"\n📄 Documentos procesados:")
        for doc in index['documents']:
            print(f"   - {doc['title']}: {doc['article_count']} artículos")
        
        # Temas más comunes
        theme_count = {}
        for article in articles_db['articles']:
            for theme in article.get('themes', []):
                theme_count[theme] = theme_count.get(theme, 0) + 1
        
        print(f"\n🏷️  Top 10 temas más frecuentes:")
        sorted_themes = sorted(theme_count.items(), key=lambda x: x[1], reverse=True)
        for theme, count in sorted_themes[:10]:
            print(f"   - {theme}: {count} artículos")
        
    except Exception as e:
        print(f"Error: {e}")

def example_custom_integration():
    """Ejemplo de integración personalizada"""
    print("\n🔧 Ejemplo 5: Integración personalizada")
    print("-" * 50)
    
    class LegalAssistant:
        """Asistente legal personalizado"""
        
        def __init__(self, groq_api_key, kb_folder="knowledge_base"):
            self.query_system = LegalQuerySystem(groq_api_key, kb_folder)
            self.conversation_history = []
        
        def ask(self, question, context_limit=5):
            """Hace una pregunta con contexto limitado"""
            # Buscar artículos relevantes
            relevant_articles = self.query_system.search_articles_by_content(question)
            
            # Limitar contexto para respuestas más rápidas
            limited_context = relevant_articles[:context_limit]
            
            # Generar respuesta
            response = self.query_system.answer_legal_question(question, limited_context)
            
            # Guardar en historial
            self.conversation_history.append({
                "question": question,
                "response": response,
                "articles_used": len(limited_context)
            })
            
            return response
        
        def get_summary(self):
            """Obtiene resumen de la conversación"""
            return {
                "total_questions": len(self.conversation_history),
                "questions": [item["question"] for item in self.conversation_history]
            }
    
    # Usar el asistente personalizado
    groq_api_key = os.getenv("GROQ_API_KEY", "tu_api_key_aqui")
    
    if groq_api_key == "tu_api_key_aqui":
        print("⚠️  Configura tu GROQ_API_KEY")
        return
    
    try:
        assistant = LegalAssistant(groq_api_key)
        
        # Hacer algunas preguntas
        questions = [
            "¿Qué es un contrato laboral?",
            "¿Cuáles son las obligaciones del empleador?"
        ]
        
        for question in questions:
            print(f"\n❓ {question}")
            response = assistant.ask(question)
            print(f"🤖 {response[:200]}...")
        
        # Mostrar resumen
        summary = assistant.get_summary()
        print(f"\n📋 Resumen de la sesión:")
        print(f"   - Preguntas realizadas: {summary['total_questions']}")
        
    except Exception as e:
        print(f"Error: {e}")

def main():
    """Ejecuta todos los ejemplos"""
    print("🏛️  Ejemplos de uso de la Base de Conocimiento Legal")
    print("=" * 70)
    
    # Verificar que existe la base de conocimiento
    if not Path("knowledge_base").exists():
        print("❌ No se encontró la carpeta 'knowledge_base'")
        print("💡 Primero ejecuta 'legal_knowledge_base.py' para crear la base de conocimiento")
        return
    
    # Ejecutar ejemplos
    examples = [
        example_basic_usage,
        example_search_by_theme,
        example_batch_queries,
        example_knowledge_base_stats,
        example_custom_integration
    ]
    
    for example_func in examples:
        try:
            example_func()
        except KeyboardInterrupt:
            print("\n⏸️  Interrumpido por el usuario")
            break
        except Exception as e:
            print(f"\n❌ Error en ejemplo: {e}")
            continue
    
    print("\n✅ Ejemplos completados")
    print("\n💡 Para usar en tu aplicación:")
    print("   from legal_query_system import LegalQuerySystem")
    print("   query_system = LegalQuerySystem(api_key, 'knowledge_base')")
    print("   response = query_system.answer_legal_question('tu pregunta')")

if __name__ == "__main__":
    main()
