"""
Ejemplos prácticos de ScrapegraphAI con Groq
Basado en la documentación oficial de Context7
"""

from scrapegraphai.graphs import SmartScraperGraph
from pydantic import BaseModel, Field
from typing import List
import json

# Configuración base con Groq
BASE_CONFIG = {
    "llm": {
        "model": "groq/llama3-8b-8192",
        "api_key": "TU_GROQ_API_KEY_AQUI",  # Reemplaza con tu API key
        "temperature": 0.1,
    },
    "verbose": True,
    "headless": True,
    "timeout": 30,
}

# Ejemplo 1: Extracción básica de información de empresa
def ejemplo_info_empresa():
    """Extrae información básica de una empresa"""
    print("📊 Ejemplo 1: Información de empresa")
    
    scraper = SmartScraperGraph(
        prompt="Extrae el nombre de la empresa, descripción, servicios principales y información de contacto",
        source="https://scrapegraphai.com/",
        config=BASE_CONFIG
    )
    
    try:
        result = scraper.run()
        print("✅ Resultado:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        return result
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

# Ejemplo 2: Usando esquemas Pydantic para datos estructurados
class ArticleInfo(BaseModel):
    title: str = Field(description="Título del artículo")
    author: str = Field(description="Autor del artículo")
    content: str = Field(description="Contenido principal")
    publish_date: str = Field(description="Fecha de publicación")

def ejemplo_con_esquema():
    """Extrae artículos usando un esquema estructurado"""
    print("📰 Ejemplo 2: Extracción con esquema Pydantic")
    
    # Nota: ScrapegraphAI local no soporta esquemas Pydantic directamente
    # Este es un ejemplo de cómo se haría con la API
    scraper = SmartScraperGraph(
        prompt="""Extrae información del artículo incluyendo:
        - Título principal
        - Autor (si está disponible)
        - Contenido principal del artículo
        - Fecha de publicación (si está disponible)
        Formatea la respuesta como JSON con las claves: title, author, content, publish_date""",
        source="https://example.com/article",
        config=BASE_CONFIG
    )
    
    try:
        result = scraper.run()
        print("✅ Resultado estructurado:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        return result
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

# Ejemplo 3: Extracción de productos de e-commerce
def ejemplo_productos():
    """Extrae información de productos"""
    print("🛒 Ejemplo 3: Productos de e-commerce")
    
    scraper = SmartScraperGraph(
        prompt="""Extrae información de productos incluyendo:
        - Nombre del producto
        - Precio
        - Descripción
        - Características principales
        - Disponibilidad
        Formatea como JSON con una lista de productos""",
        source="https://example-store.com/products",
        config=BASE_CONFIG
    )
    
    try:
        result = scraper.run()
        print("✅ Productos extraídos:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        return result
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

# Ejemplo 4: Configuración avanzada con headers personalizados
def ejemplo_headers_personalizados():
    """Ejemplo con headers personalizados para evitar bloqueos"""
    print("🔧 Ejemplo 4: Headers personalizados")
    
    config_avanzado = {
        "llm": {
            "model": "groq/llama3-70b-8192",  # Modelo más potente
            "api_key": "TU_GROQ_API_KEY_AQUI",
            "temperature": 0,
        },
        "verbose": True,
        "headless": True,
        "timeout": 45,
        # Headers personalizados para parecer un navegador real
        "headers": {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "es-ES,es;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Cache-Control": "no-cache",
        }
    }
    
    scraper = SmartScraperGraph(
        prompt="Extrae el título principal y los primeros 3 párrafos de contenido",
        source="https://news.ycombinator.com/",
        config=config_avanzado
    )
    
    try:
        result = scraper.run()
        print("✅ Resultado con headers personalizados:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        return result
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

# Ejemplo 5: Manejo de errores y reintentos
def ejemplo_con_reintentos():
    """Ejemplo con manejo robusto de errores"""
    print("🔄 Ejemplo 5: Manejo de errores y reintentos")
    
    urls_prueba = [
        "https://scrapegraphai.com/",
        "https://github.com/VinciGit00/Scrapegraph-ai",
        "https://pypi.org/project/scrapegraphai/",
    ]
    
    for i, url in enumerate(urls_prueba, 1):
        print(f"\n🔍 Intento {i}: {url}")
        
        scraper = SmartScraperGraph(
            prompt="Extrae el título principal y una breve descripción",
            source=url,
            config=BASE_CONFIG
        )
        
        try:
            result = scraper.run()
            print(f"✅ Éxito en intento {i}:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            return result  # Salir en el primer éxito
            
        except Exception as e:
            print(f"❌ Error en intento {i}: {e}")
            if i == len(urls_prueba):
                print("😞 Todos los intentos fallaron")
            continue
    
    return None

def main():
    """Ejecuta todos los ejemplos"""
    print("🚀 Ejemplos de ScrapegraphAI con Groq")
    print("=" * 60)
    print()
    
    # Verificar que se haya configurado la API key
    if "TU_GROQ_API_KEY_AQUI" in BASE_CONFIG["llm"]["api_key"]:
        print("⚠️  IMPORTANTE: Configura tu API key de Groq primero")
        print("🔧 Ejecuta: python setup_groq.py")
        print("🌐 O ve a: https://console.groq.com/")
        return
    
    ejemplos = [
        ("Información de empresa", ejemplo_info_empresa),
        ("Esquema estructurado", ejemplo_con_esquema),
        ("Productos e-commerce", ejemplo_productos),
        ("Headers personalizados", ejemplo_headers_personalizados),
        ("Manejo de errores", ejemplo_con_reintentos),
    ]
    
    for nombre, funcion in ejemplos:
        print(f"\n{'='*60}")
        print(f"🎯 Ejecutando: {nombre}")
        print('='*60)
        
        try:
            resultado = funcion()
            if resultado:
                print(f"✅ {nombre} completado exitosamente")
            else:
                print(f"❌ {nombre} falló")
        except Exception as e:
            print(f"💥 Error inesperado en {nombre}: {e}")
        
        input("\n⏸️  Presiona Enter para continuar al siguiente ejemplo...")

if __name__ == "__main__":
    main()
