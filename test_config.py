import requests
import json

def test_ollama():
    """Verifica si Ollama está disponible localmente"""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print("✅ Ollama está disponible!")
            print("Modelos instalados:")
            for model in models:
                print(f"  - {model['name']}")
            return True
        else:
            print("❌ Ollama no responde correctamente")
            return False
    except Exception as e:
        print(f"❌ Ollama no está disponible: {e}")
        return False

def test_openai_key(api_key):
    """Verifica si la API key de OpenAI es válida"""
    if not api_key or api_key.startswith("sk-proj-VnyY5T"):
        print("❌ API key de OpenAI no válida (parece ser de ejemplo)")
        return False
    
    # Aquí podrías hacer una llamada de prueba a OpenAI
    print("⚠️  No se puede verificar la API key sin hacer una llamada real")
    return False

def main():
    print("🔍 Verificando opciones disponibles para ScrapegraphAI...\n")
    
    # Verificar Ollama
    ollama_available = test_ollama()
    
    # Verificar OpenAI
    api_key = "********************************************************************************************************************************************************************"
    openai_available = test_openai_key(api_key)
    
    print("\n📋 Resumen:")
    if ollama_available:
        print("✅ Recomendación: Usar Ollama (GRATIS)")
        print("   Tu app.py ya está configurado para Ollama")
    elif not ollama_available and not openai_available:
        print("❌ Necesitas instalar Ollama o conseguir una API key válida de OpenAI")
        print("   Instrucciones en install_ollama.md")
    
    print("\n🚀 Próximos pasos:")
    if not ollama_available:
        print("1. Instalar Ollama desde: https://ollama.com/download")
        print("2. Ejecutar: ollama pull llama3.2")
        print("3. Ejecutar: python app.py")
    else:
        print("1. Ejecutar: python app.py")

if __name__ == "__main__":
    main()
