"""
Script para configurar Groq con ScrapegraphAI
Groq es GRATUITO y mucho más rápido que OpenAI
"""

import os
import json
import requests
from scrapegraphai.graphs import SmartScraperGraph

def test_groq_connection(api_key):
    """Prueba la conexión con Groq"""
    print("🔍 Probando conexión con Groq...")
    
    # Configuración de prueba
    test_config = {
        "llm": {
            "model": "groq/llama3-8b-8192",
            "api_key": api_key,
            "temperature": 0,
        },
        "verbose": False,
        "headless": True,
        "timeout": 15,
    }
    
    try:
        # Prueba simple
        test_scraper = SmartScraperGraph(
            prompt="¿Cuál es el título de esta página?",
            source="https://example.com",
            config=test_config
        )
        
        result = test_scraper.run()
        print("✅ Conexión con Groq exitosa!")
        print(f"📄 Resultado de prueba: {result}")
        return True
        
    except Exception as e:
        print(f"❌ Error de conexión: {e}")
        return False

def get_groq_models():
    """Obtiene la lista de modelos disponibles en Groq"""
    models = [
        "groq/llama3-8b-8192",      # Rápido y eficiente
        "groq/llama3-70b-8192",     # Más potente
        "groq/mixtral-8x7b-32768",  # Bueno para tareas complejas
        "groq/gemma-7b-it",         # Alternativa de Google
    ]
    
    print("🤖 Modelos disponibles en Groq:")
    for i, model in enumerate(models, 1):
        print(f"  {i}. {model}")
    
    return models

def setup_api_key():
    """Guía para configurar la API key"""
    print("🔑 Configuración de API Key de Groq")
    print("=" * 50)
    print("1. Ve a: https://console.groq.com/")
    print("2. Regístrate gratis (solo necesitas email)")
    print("3. Ve a 'API Keys' en el menú lateral")
    print("4. Haz clic en 'Create API Key'")
    print("5. Copia la API key generada")
    print()
    
    api_key = input("📝 Pega tu API key de Groq aquí: ").strip()
    
    if not api_key:
        print("❌ No se proporcionó API key")
        return None
    
    if not api_key.startswith("gsk_"):
        print("⚠️  La API key de Groq normalmente empieza con 'gsk_'")
        confirm = input("¿Continuar de todos modos? (y/n): ")
        if confirm.lower() != 'y':
            return None
    
    return api_key

def update_app_py(api_key):
    """Actualiza app.py con la API key"""
    try:
        with open("app.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Reemplazar la API key placeholder
        updated_content = content.replace(
            '"TU_GROQ_API_KEY_AQUI"',
            f'"{api_key}"'
        )
        
        with open("app.py", "w", encoding="utf-8") as f:
            f.write(updated_content)
        
        print("✅ app.py actualizado con tu API key")
        return True
        
    except Exception as e:
        print(f"❌ Error actualizando app.py: {e}")
        return False

def create_env_file(api_key):
    """Crea archivo .env para mayor seguridad"""
    try:
        with open(".env", "w") as f:
            f.write(f"GROQ_API_KEY={api_key}\n")
        
        print("✅ Archivo .env creado")
        print("💡 Tip: Agrega .env a tu .gitignore para no subir la API key a Git")
        return True
        
    except Exception as e:
        print(f"❌ Error creando .env: {e}")
        return False

def main():
    print("🚀 Configurador de Groq para ScrapegraphAI")
    print("=" * 60)
    print()
    
    # Mostrar modelos disponibles
    models = get_groq_models()
    print()
    
    # Configurar API key
    api_key = setup_api_key()
    if not api_key:
        print("❌ Configuración cancelada")
        return
    
    print("\n🔧 Configurando...")
    
    # Probar conexión
    if test_groq_connection(api_key):
        print("\n📝 Actualizando archivos...")
        
        # Actualizar app.py
        if update_app_py(api_key):
            print("✅ app.py configurado")
        
        # Crear .env
        if create_env_file(api_key):
            print("✅ .env creado")
        
        print("\n🎉 ¡Configuración completada!")
        print("🚀 Ahora puedes ejecutar: python app.py")
        
        # Preguntar si quiere ejecutar ahora
        run_now = input("\n¿Ejecutar app.py ahora? (y/n): ")
        if run_now.lower() == 'y':
            print("\n" + "="*60)
            os.system("python app.py")
    
    else:
        print("\n❌ No se pudo establecer conexión con Groq")
        print("🔧 Verifica tu API key y conexión a internet")

if __name__ == "__main__":
    main()
