# 🏛️ Base de Conocimiento Legal con ScrapegraphAI + Groq

Una herramienta completa para procesar documentos legales (PDFs) y crear una base de conocimiento estructurada que puede ser consultada por LLMs.

## 🎯 ¿Qué hace esta herramienta?

- **Procesa múltiples PDFs** de leyes, decretos, reglamentos
- **Extrae artículos** y estructura legal automáticamente
- **Identifica temáticas** y palabras clave
- **Crea base de conocimiento** consultable por LLMs
- **Sistema de consultas** inteligente con IA

## ⚡ Características principales

- ✅ **Procesamiento automático** de documentos legales
- ✅ **Extracción estructurada** de artículos y contenido
- ✅ **Identificación de temas** y clasificación
- ✅ **Base de datos** JSON para fácil integración
- ✅ **Sistema de consultas** con IA conversacional
- ✅ **Búsqueda por contenido** y temáticas
- ✅ **Respuestas fundamentadas** con citas legales

## 🚀 Instalación rápida

### 1. Configuración automática
```bash
python setup_legal_kb.py
```

### 2. Configuración manual
```bash
# Instalar dependencias
pip install scrapegraphai groq pydantic

# Instalar navegadores para ScrapegraphAI
playwright install

# Crear estructura de carpetas
mkdir pdfs_leyes knowledge_base
```

## 🔑 Configuración de Groq

1. **Obtén API key gratuita:**
   - Ve a [console.groq.com](https://console.groq.com/)
   - Regístrate gratis (solo email)
   - Crea una API key

2. **Configura el archivo .env:**
```bash
GROQ_API_KEY=tu_api_key_aqui
INPUT_FOLDER=pdfs_leyes
OUTPUT_FOLDER=knowledge_base
```

## 📁 Estructura del proyecto

```
legal-knowledge-base/
├── legal_knowledge_base.py    # Procesador principal
├── legal_query_system.py      # Sistema de consultas
├── setup_legal_kb.py          # Configurador automático
├── .env                       # Configuración (crear)
├── pdfs_leyes/               # PDFs a procesar
│   ├── ley_trabajo.pdf
│   ├── codigo_civil.pdf
│   └── README.md
├── knowledge_base/           # Base de conocimiento generada
│   ├── knowledge_base_index.json
│   ├── articles_database.json
│   ├── processing_report.json
│   └── [documento]_processed.json
└── examples/
    └── README.md
```

## 🎯 Uso paso a paso

### Paso 1: Preparar documentos
```bash
# Coloca tus PDFs en la carpeta
cp mis_leyes/*.pdf pdfs_leyes/
```

### Paso 2: Procesar documentos
```bash
python legal_knowledge_base.py
```

**Ejemplo de ejecución:**
```
🏛️  Herramienta de Construcción de Base de Conocimiento Legal
🔑 Ingresa tu API key de Groq: gsk_...
📁 Carpeta con PDFs: pdfs_leyes
💾 Carpeta de salida: knowledge_base

📄 Encontrados 3 archivos PDF:
   - codigo_civil.pdf
   - ley_trabajo.pdf
   - decreto_123.pdf

🚀 Iniciando procesamiento...
✅ Procesado exitosamente: codigo_civil.pdf
   - Artículos extraídos: 245
   - Temas identificados: 15
```

### Paso 3: Consultar la base de conocimiento
```bash
python legal_query_system.py
```

**Ejemplo de consulta:**
```
🔍 Tu consulta: ¿Qué dice la ley sobre contratos laborales?

📋 Respuesta:
Según los artículos analizados en la base de conocimiento:

**Artículo 15 de la Ley de Trabajo:**
Los contratos laborales deben establecer claramente...

**Artículo 23:**
La duración máxima de la jornada laboral...

**Referencias:**
- Ley de Trabajo, Artículos 15, 23, 45
- Código Civil, Artículo 102
```

## 📊 Archivos generados

### 1. `knowledge_base_index.json`
Índice general con resumen de todos los documentos:
```json
{
  "total_documents": 3,
  "total_articles": 456,
  "all_themes": ["contratos", "laboral", "civil", ...],
  "documents": [...]
}
```

### 2. `articles_database.json`
Base de datos consolidada de todos los artículos:
```json
{
  "total_articles": 456,
  "articles": [
    {
      "source_document": "ley_trabajo.pdf",
      "article_number": "Artículo 15",
      "content": "Los contratos laborales...",
      "themes": ["contratos", "laboral"],
      "keywords": ["empleado", "empleador", "salario"]
    }
  ]
}
```

### 3. `[documento]_processed.json`
Cada documento procesado individualmente con su estructura completa.

## 🔍 Comandos de consulta

En el sistema interactivo puedes usar:

- **`temas`** - Ver todos los temas disponibles
- **`documentos`** - Lista de documentos procesados
- **`buscar [tema]`** - Buscar artículos por tema
- **Pregunta directa** - "¿Qué dice sobre...?"
- **`salir`** - Terminar sesión

## 🎨 Ejemplos de preguntas

### Consultas generales:
- "¿Qué dice la ley sobre contratos laborales?"
- "¿Cuáles son las sanciones por incumplimiento?"
- "¿Qué artículos hablan sobre derechos del consumidor?"

### Búsquedas específicas:
- "¿Cuál es el plazo para presentar una demanda?"
- "¿Qué requisitos debe cumplir un contrato?"
- "¿Cuáles son las obligaciones del empleador?"

### Búsquedas por tema:
- `buscar contratos`
- `buscar sanciones`
- `buscar derechos`

## 🔧 Configuración avanzada

### Modelos de Groq disponibles:
```python
# En legal_knowledge_base.py, línea ~67
"model": "groq/llama3-8b-8192",    # Rápido
"model": "groq/llama3-70b-8192",   # Más potente (recomendado)
"model": "groq/mixtral-8x7b-32768", # Alternativa
```

### Personalizar prompts:
Edita los prompts en `legal_knowledge_base.py` línea ~95 para adaptar la extracción a tus necesidades específicas.

## 📋 Requisitos de PDFs

### ✅ PDFs compatibles:
- Texto seleccionable (no imágenes escaneadas)
- Documentos legales estructurados
- Tamaño razonable (<50MB)
- Formato estándar de leyes/decretos

### ❌ PDFs no compatibles:
- Imágenes escaneadas sin OCR
- PDFs corruptos o protegidos
- Documentos sin estructura clara

## 🚨 Solución de problemas

### Error: "No se pudo extraer contenido"
- Verifica que el PDF tenga texto seleccionable
- Prueba con un PDF más simple primero
- Revisa que la API key de Groq sea válida

### Error: "Rate limit exceeded"
- Groq tiene límites generosos pero no ilimitados
- Agrega pausas entre documentos (ya incluidas)
- Considera procesar en lotes más pequeños

### Error: "Timeout"
- Aumenta el timeout en la configuración
- Divide documentos muy grandes
- Verifica tu conexión a internet

## 💡 Tips y mejores prácticas

1. **Nombres descriptivos**: Usa nombres claros para tus PDFs
2. **Documentos estructurados**: Funciona mejor con leyes bien organizadas
3. **Lotes pequeños**: Procesa 5-10 documentos a la vez
4. **Verificar resultados**: Revisa los JSON generados
5. **Backup**: Guarda copias de tu base de conocimiento

## 🤝 Integración con otros LLMs

La base de conocimiento generada es compatible con:

- **ChatGPT**: Copia el contenido de `articles_database.json`
- **Claude**: Usa los artículos como contexto
- **LLMs locales**: Integra directamente los JSON
- **RAG systems**: Usa como fuente de documentos

## 📄 Licencia

Este proyecto es de código abierto. Úsalo libremente para fines educativos y profesionales.

## 🆘 Soporte

¿Problemas o preguntas?
1. Revisa este README completo
2. Ejecuta `python setup_legal_kb.py` para verificar configuración
3. Revisa los logs de error en la consola

---

**¡Construye tu base de conocimiento legal con IA! ⚖️✨**
