# ScrapegraphAI con Groq - Guía Completa

Este proyecto demuestra cómo usar **ScrapegraphAI** con **Groq** para web scraping inteligente usando IA.

## 🚀 ¿Por qué Groq?

- ✅ **GRATUITO** con límites generosos
- ⚡ **<PERSON><PERSON><PERSON> r<PERSON>pid<PERSON>** (hasta 10x más rápido que OpenAI)
- 🤖 **Modelos potentes** (Llama 3, Mixtral, Gemma)
- 🔑 **F<PERSON>cil de configurar** (solo necesitas email)

## 📋 Requisitos

- Python 3.8+
- Cuenta gratuita en Groq
- Conexión a internet

## 🛠️ Instalación

1. **Clona o descarga este proyecto**
2. **Instala las dependencias:**
   ```bash
   pip install scrapegraphai groq
   ```

## 🔑 Configuración de Groq (GRATIS)

### Opción 1: Configuración automática
```bash
python setup_groq.py
```

### Opción 2: Configuración manual
1. Ve a [console.groq.com](https://console.groq.com/)
2. Regístrate gratis (solo email)
3. Ve a "API Keys" → "Create API Key"
4. Copia la API key
5. Reemplaza `TU_GROQ_API_KEY_AQUI` en `app.py`

## 🎯 Uso

### Scraping básico
```bash
python app.py
```

### Ejemplos avanzados
```bash
python ejemplos_scrapegraphai.py
```

## 📁 Archivos del proyecto

- `app.py` - Script principal con Groq
- `setup_groq.py` - Configurador automático
- `ejemplos_scrapegraphai.py` - Ejemplos avanzados
- `app_simple.py` - Versión simple que funciona

## 🔧 Configuración avanzada

### Modelos disponibles en Groq:
- `groq/llama3-8b-8192` - Rápido y eficiente (recomendado)
- `groq/llama3-70b-8192` - Más potente
- `groq/mixtral-8x7b-32768` - Bueno para tareas complejas
- `groq/gemma-7b-it` - Alternativa de Google

### Ejemplo de configuración:
```python
graph_config = {
    "llm": {
        "model": "groq/llama3-8b-8192",
        "api_key": "tu_api_key_aqui",
        "temperature": 0.1,
    },
    "verbose": True,
    "headless": True,
    "timeout": 30,
}
```

## 🎨 Ejemplos de prompts

### Información de empresa:
```python
prompt = "Extrae el nombre de la empresa, descripción, servicios y contacto"
```

### Productos e-commerce:
```python
prompt = """Extrae información de productos:
- Nombre y precio
- Descripción
- Características
- Disponibilidad"""
```

### Artículos de noticias:
```python
prompt = "Extrae título, autor, fecha y contenido principal del artículo"
```

## 🚨 Solución de problemas

### Error: "Incorrect API key"
- Verifica que tu API key de Groq sea correcta
- Asegúrate de que empiece con `gsk_`

### Error: "Rate limit exceeded"
- Groq tiene límites gratuitos generosos
- Espera unos minutos y vuelve a intentar

### Error: "Timeout"
- Aumenta el valor de `timeout` en la configuración
- Prueba con una URL más simple

### Error: "Failed to scrape"
- Verifica tu conexión a internet
- Prueba con `headless: True`
- Agrega headers personalizados

## 📊 Comparación con otras opciones

| Opción | Costo | Velocidad | Configuración |
|--------|-------|-----------|---------------|
| **Groq** | 🆓 Gratis | ⚡ Muy rápido | 🟢 Fácil |
| OpenAI | 💰 De pago | 🐌 Lento | 🟢 Fácil |
| Ollama | 🆓 Gratis | 🐌 Lento | 🔴 Complejo |

## 🔗 Enlaces útiles

- [Groq Console](https://console.groq.com/) - Obtén tu API key
- [ScrapegraphAI Docs](https://scrapegraphai.com/) - Documentación oficial
- [Groq Models](https://console.groq.com/docs/models) - Lista de modelos

## 💡 Tips y mejores prácticas

1. **Usa prompts específicos** - Mientras más detallado, mejor resultado
2. **Configura timeout apropiado** - 30-60 segundos para sitios complejos
3. **Usa headless: true** - Evita problemas con el navegador
4. **Agrega headers** - Para evitar bloqueos anti-bot
5. **Maneja errores** - Siempre incluye try/catch

## 🤝 Contribuir

¿Encontraste un bug o tienes una mejora? ¡Abre un issue o pull request!

## 📄 Licencia

Este proyecto es de código abierto. Úsalo libremente para aprender y desarrollar.

---

**¡Feliz scraping! 🕷️✨**
