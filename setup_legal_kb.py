"""
Script de configuración para la Herramienta de Base de Conocimiento Legal
Configura el entorno y verifica dependencias
"""

import os
import sys
import subprocess
from pathlib import Path
import requests

def check_python_version():
    """Verifica la versión de Python"""
    print("🐍 Verificando versión de Python...")
    
    if sys.version_info < (3, 8):
        print("❌ Se requiere Python 3.8 o superior")
        print(f"   Versión actual: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - OK")
    return True

def install_dependencies():
    """Instala las dependencias necesarias"""
    print("\n📦 Instalando dependencias...")
    
    dependencies = [
        "scrapegraphai",
        "groq",
        "pydantic",
        "pathlib",
        "requests"
    ]
    
    for dep in dependencies:
        try:
            print(f"   Instalando {dep}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep], 
                                stdout=subprocess.DEVNULL, 
                                stderr=subprocess.DEVNULL)
            print(f"   ✅ {dep} instalado")
        except subprocess.CalledProcessError:
            print(f"   ❌ Error instalando {dep}")
            return False
    
    # Instalar Playwright para ScrapegraphAI
    try:
        print("   Instalando navegadores de Playwright...")
        subprocess.check_call([sys.executable, "-m", "playwright", "install"], 
                            stdout=subprocess.DEVNULL, 
                            stderr=subprocess.DEVNULL)
        print("   ✅ Playwright configurado")
    except subprocess.CalledProcessError:
        print("   ⚠️  Error con Playwright (opcional)")
    
    return True

def test_groq_connection():
    """Prueba la conexión con Groq"""
    print("\n🔑 Configuración de Groq...")
    
    api_key = input("Ingresa tu API key de Groq (o presiona Enter para omitir): ").strip()
    
    if not api_key:
        print("⚠️  API key omitida - configúrala más tarde")
        return True
    
    try:
        # Prueba simple de conexión
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # Nota: Esta es una prueba básica, Groq puede tener diferentes endpoints
        print("   Verificando API key...")
        
        # Guardar API key en archivo de configuración
        config_content = f"""# Configuración de la Base de Conocimiento Legal
GROQ_API_KEY={api_key}

# Carpetas
INPUT_FOLDER=pdfs_leyes
OUTPUT_FOLDER=knowledge_base
"""
        
        with open(".env", "w") as f:
            f.write(config_content)
        
        print("   ✅ API key guardada en .env")
        return True
        
    except Exception as e:
        print(f"   ⚠️  No se pudo verificar la API key: {e}")
        return True  # No es crítico para la configuración

def create_folder_structure():
    """Crea la estructura de carpetas necesaria"""
    print("\n📁 Creando estructura de carpetas...")
    
    folders = [
        "pdfs_leyes",
        "knowledge_base",
        "examples"
    ]
    
    for folder in folders:
        Path(folder).mkdir(exist_ok=True)
        print(f"   ✅ {folder}/")
    
    # Crear archivo de ejemplo
    example_content = """# Ejemplos de uso

## 1. Procesar PDFs de leyes
```bash
python legal_knowledge_base.py
```

## 2. Consultar la base de conocimiento
```bash
python legal_query_system.py
```

## 3. Ejemplos de preguntas:
- "¿Qué dice la ley sobre contratos laborales?"
- "¿Cuáles son las sanciones por incumplimiento?"
- "¿Qué artículos hablan sobre derechos del consumidor?"
"""
    
    with open("examples/README.md", "w", encoding="utf-8") as f:
        f.write(example_content)
    
    print("   ✅ examples/README.md creado")

def create_sample_files():
    """Crea archivos de ejemplo y documentación"""
    print("\n📄 Creando archivos de ejemplo...")
    
    # Crear archivo de configuración de ejemplo
    config_example = """# Archivo de configuración (.env)
# Copia este archivo como .env y completa los valores

# API Key de Groq (obligatorio)
GROQ_API_KEY=tu_api_key_aqui

# Carpetas (opcional, usa los valores por defecto si no se especifican)
INPUT_FOLDER=pdfs_leyes
OUTPUT_FOLDER=knowledge_base

# Configuración del modelo (opcional)
GROQ_MODEL=groq/llama3-70b-8192
TEMPERATURE=0.1
"""
    
    with open(".env.example", "w", encoding="utf-8") as f:
        f.write(config_example)
    
    print("   ✅ .env.example creado")
    
    # Crear instrucciones para PDFs
    pdf_instructions = """# Instrucciones para PDFs

## Formatos soportados:
- PDF con texto seleccionable (no imágenes escaneadas)
- Documentos legales estructurados
- Leyes, decretos, reglamentos, etc.

## Cómo preparar tus PDFs:
1. Coloca todos los PDFs en la carpeta 'pdfs_leyes/'
2. Usa nombres descriptivos: 'ley_trabajo_2023.pdf'
3. Asegúrate de que el texto sea seleccionable
4. Evita PDFs muy grandes (>50MB)

## Ejemplos de nombres recomendados:
- codigo_civil.pdf
- ley_proteccion_datos.pdf
- decreto_supremo_123.pdf
- reglamento_transito.pdf
"""
    
    with open("pdfs_leyes/README.md", "w", encoding="utf-8") as f:
        f.write(pdf_instructions)
    
    print("   ✅ pdfs_leyes/README.md creado")

def run_tests():
    """Ejecuta pruebas básicas del sistema"""
    print("\n🧪 Ejecutando pruebas básicas...")
    
    try:
        # Probar importaciones
        print("   Probando importaciones...")
        import scrapegraphai
        print("   ✅ scrapegraphai importado")
        
        from pydantic import BaseModel
        print("   ✅ pydantic importado")
        
        import json
        print("   ✅ json disponible")
        
        # Probar creación de carpetas
        test_path = Path("test_folder")
        test_path.mkdir(exist_ok=True)
        test_path.rmdir()
        print("   ✅ Sistema de archivos funcional")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Error en pruebas: {e}")
        return False

def show_next_steps():
    """Muestra los próximos pasos"""
    print("\n🎉 ¡Configuración completada!")
    print("\n📋 Próximos pasos:")
    print("1. 🔑 Obtén una API key gratuita de Groq:")
    print("   - Ve a: https://console.groq.com/")
    print("   - Regístrate gratis")
    print("   - Crea una API key")
    print("   - Guárdala en el archivo .env")
    print()
    print("2. 📄 Coloca tus PDFs de leyes:")
    print("   - Copia los PDFs a la carpeta 'pdfs_leyes/'")
    print("   - Asegúrate de que sean PDFs con texto seleccionable")
    print()
    print("3. 🚀 Ejecuta el procesamiento:")
    print("   python legal_knowledge_base.py")
    print()
    print("4. 🔍 Consulta la base de conocimiento:")
    print("   python legal_query_system.py")
    print()
    print("📚 Para más información, revisa:")
    print("   - README_legal_kb.md")
    print("   - examples/README.md")
    print("   - pdfs_leyes/README.md")

def main():
    """Función principal de configuración"""
    print("🏛️  Configurador de Base de Conocimiento Legal")
    print("=" * 60)
    print("Este script configurará todo lo necesario para procesar")
    print("documentos legales con ScrapegraphAI + Groq")
    print()
    
    # Verificaciones y configuración
    steps = [
        ("Verificar Python", check_python_version),
        ("Instalar dependencias", install_dependencies),
        ("Configurar Groq", test_groq_connection),
        ("Crear carpetas", create_folder_structure),
        ("Crear ejemplos", create_sample_files),
        ("Ejecutar pruebas", run_tests),
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔧 {step_name}...")
        if not step_func():
            print(f"❌ Error en: {step_name}")
            print("⚠️  La configuración puede estar incompleta")
            break
    else:
        # Si todos los pasos fueron exitosos
        show_next_steps()

if __name__ == "__main__":
    main()
