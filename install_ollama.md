# Instalación de Ollama (Modelo Local GRATUITO)

## Paso 1: <PERSON>cargar e instalar Ollama
1. Ve a: https://ollama.com/download
2. Descarga la versión para Windows
3. Instala el programa

## Paso 2: Instalar el modelo Llama 3.2
Abre una terminal (PowerShell o CMD) y ejecuta:
```bash
ollama pull llama3.2
```

## Paso 3: Verificar que funciona
```bash
ollama list
```

Deberías ver `llama3.2` en la lista.

## Paso 4: Ejecutar tu script
Ahora puedes ejecutar `python app.py` sin necesidad de API key de OpenAI.

## Alternativa: Usar Hugging Face (también gratuito)
Si no quieres instalar Ollama, puedes usar Hugging Face:

```python
graph_config = {
   "llm": {
       "model": "huggingface/microsoft/DialoGPT-medium",
       "api_key": "tu_huggingface_token",  # Gratis en huggingface.co
   },
   "verbose": True,
   "headless": True,
   "timeout": 30,
}
```

## ¿Por qué necesita un LLM?
ScrapegraphAI usa inteligencia artificial para:
- Entender tu prompt en lenguaje natural
- Analizar el contenido HTML
- Extraer la información que solicitas
- Formatear la respuesta

Por eso necesita acceso a un modelo de lenguaje, pero puede ser local (gratis) o en la nube (de pago).
