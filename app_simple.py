from scrapegraphai.graphs import SmartScraperGraph
import json

# Define the configuration for the scraping pipeline
graph_config = {
   "llm": {
       "api_key": "********************************************************************************************************************************************************************",
       "model": "gpt-4o-mini",
   },
   "verbose": True,
   "headless": True,
   "timeout": 20,
}

# Probar con una página más simple primero
smart_scraper_graph = SmartScraperGraph(
    prompt="Extrae el título principal, descripción y cualquier información sobre instalación o uso básico de esta página web.",
    source="https://github.com/VinciGit00/Scrapegraph-ai",
    config=graph_config
)

print("Iniciando scraping...")
try:
    result = smart_scraper_graph.run()
    print("Resultado:")
    print(json.dumps(result, indent=4, ensure_ascii=False))
except Exception as e:
    print(f"Error: {e}")
    print("Intentando con una configuración más básica...")
    
    # Configuración alternativa
    basic_config = {
        "llm": {
            "api_key": "********************************************************************************************************************************************************************",
            "model": "gpt-4o-mini",
        },
        "verbose": False,
        "headless": True,
    }
    
    simple_scraper = SmartScraperGraph(
        prompt="¿Qué es ScrapegraphAI y cómo se instala?",
        source="https://pypi.org/project/scrapegraphai/",
        config=basic_config
    )
    
    try:
        result = simple_scraper.run()
        print("Resultado alternativo:")
        print(json.dumps(result, indent=4, ensure_ascii=False))
    except Exception as e2:
        print(f"Error en configuración alternativa: {e2}")
